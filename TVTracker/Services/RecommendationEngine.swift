import Foundation
import CoreData
import Combine

// MARK: - Recommendation Engine
class RecommendationEngine: ObservableObject {
    static let shared = RecommendationEngine()

    private let tmdbService = TMDBService.shared
    private let openAIService = OpenAIService.shared
    private var cancellables = Set<AnyCancellable>()

    @Published var movieRecommendations: [RecommendationItem] = []
    @Published var tvShowRecommendations: [RecommendationItem] = []
    @Published var personalizedInsights: String = ""
    @Published var isLoading = false
    @Published var lastUpdateDate: Date?

    private init() {}
    
    // MARK: - Main Recommendation Methods
    func generateRecommendations(for context: NSManagedObjectContext) async {
        await MainActor.run {
            isLoading = true
        }

        do {
            // Get user's watch history and ratings
            let userProfile = await buildUserProfile(context: context)

            // Generate AI-powered recommendations
            async let movieRecsTask = generateAIMovieRecommendations(userProfile: userProfile)
            async let tvRecsTask = generateAITVShowRecommendations(userProfile: userProfile)
            async let insightsTask = generatePersonalizedInsights(userProfile: userProfile)

            // Wait for all AI requests to complete
            let (movieRecs, tvRecs, insights) = try await (movieRecsTask, tvRecsTask, insightsTask)

            await MainActor.run {
                self.movieRecommendations = movieRecs
                self.tvShowRecommendations = tvRecs
                self.personalizedInsights = insights
                self.lastUpdateDate = Date()
                self.isLoading = false
            }
        } catch {
            print("Error generating recommendations: \(error)")
            await MainActor.run {
                self.isLoading = false
                // Fallback to trending content if AI fails
                Task {
                    await self.generateFallbackRecommendations()
                }
            }
        }
    }

    func refreshRecommendations(for context: NSManagedObjectContext) async {
        // Only refresh if it's been more than 1 hour since last update
        if let lastUpdate = lastUpdateDate,
           Date().timeIntervalSince(lastUpdate) < 3600 {
            return
        }

        await generateRecommendations(for: context)
    }
    
    // MARK: - User Profile Building
    private func buildUserProfile(context: NSManagedObjectContext) async -> UserProfile {
        return await withCheckedContinuation { continuation in
            context.perform {
                let movieRequest: NSFetchRequest<Movie> = Movie.fetchRequest()
                movieRequest.predicate = NSPredicate(format: "isWatched == YES")
                
                let tvShowRequest: NSFetchRequest<TVShow> = TVShow.fetchRequest()
                tvShowRequest.predicate = NSPredicate(format: "isWatched == YES")
                
                do {
                    let watchedMovies = try context.fetch(movieRequest)
                    let watchedTVShows = try context.fetch(tvShowRequest)
                    
                    let profile = UserProfile(
                        watchedMovies: watchedMovies,
                        watchedTVShows: watchedTVShows
                    )
                    
                    continuation.resume(returning: profile)
                } catch {
                    print("Error fetching user data: \(error)")
                    continuation.resume(returning: UserProfile(watchedMovies: [], watchedTVShows: []))
                }
            }
        }
    }
    
    // MARK: - AI-Powered Recommendations
    private func generateAIMovieRecommendations(userProfile: UserProfile) async throws -> [RecommendationItem] {
        // If user has no watch history, return trending movies as fallback
        if userProfile.watchedMovies.isEmpty {
            return try await generateFallbackMovieRecommendations()
        }

        // Get AI recommendations
        let aiRecommendations = try await openAIService.generateMovieRecommendations(userProfile: userProfile)

        // Convert AI recommendations to RecommendationItem objects
        var recommendations: [RecommendationItem] = []

        for (index, recommendation) in aiRecommendations.enumerated() {
            let item = RecommendationItem(
                id: index,
                title: extractTitle(from: recommendation),
                description: extractDescription(from: recommendation),
                type: .movie,
                aiGenerated: true,
                confidence: calculateConfidence(for: recommendation, userProfile: userProfile)
            )
            recommendations.append(item)
        }

        return recommendations
    }

    private func generateFallbackMovieRecommendations() async throws -> [RecommendationItem] {
        let trendingMovies = try await tmdbService.fetchTrendingMovies()

        return trendingMovies.prefix(10).enumerated().map { index, movie in
            RecommendationItem(
                id: index,
                title: movie.title,
                description: "Trending movie with \(String(format: "%.1f", movie.voteAverage)) rating",
                type: .movie,
                aiGenerated: false,
                confidence: 0.7
            )
        }
    }
    
    private func generateAITVShowRecommendations(userProfile: UserProfile) async throws -> [RecommendationItem] {
        // If user has no watch history, return trending TV shows as fallback
        if userProfile.watchedTVShows.isEmpty {
            return try await generateFallbackTVShowRecommendations()
        }

        // Get AI recommendations
        let aiRecommendations = try await openAIService.generateTVShowRecommendations(userProfile: userProfile)

        // Convert AI recommendations to RecommendationItem objects
        var recommendations: [RecommendationItem] = []

        for (index, recommendation) in aiRecommendations.enumerated() {
            let item = RecommendationItem(
                id: index,
                title: extractTitle(from: recommendation),
                description: extractDescription(from: recommendation),
                type: .tvShow,
                aiGenerated: true,
                confidence: calculateConfidence(for: recommendation, userProfile: userProfile)
            )
            recommendations.append(item)
        }

        return recommendations
    }

    private func generateFallbackTVShowRecommendations() async throws -> [RecommendationItem] {
        let trendingTVShows = try await tmdbService.fetchTrendingTVShows()

        return trendingTVShows.prefix(10).enumerated().map { index, tvShow in
            RecommendationItem(
                id: index,
                title: tvShow.name,
                description: "Trending TV show with \(String(format: "%.1f", tvShow.voteAverage)) rating",
                type: .tvShow,
                aiGenerated: false,
                confidence: 0.7
            )
        }
    }

    private func generatePersonalizedInsights(userProfile: UserProfile) async throws -> String {
        // If user has minimal watch history, provide generic insights
        if userProfile.watchedMovies.count + userProfile.watchedTVShows.count < 3 {
            return "Start watching and rating content to get personalized insights about your viewing preferences!"
        }

        return try await openAIService.generatePersonalizedInsights(userProfile: userProfile)
    }

    private func generateFallbackRecommendations() async {
        do {
            async let movieTask = generateFallbackMovieRecommendations()
            async let tvTask = generateFallbackTVShowRecommendations()

            let (movies, tvShows) = try await (movieTask, tvTask)

            await MainActor.run {
                self.movieRecommendations = movies
                self.tvShowRecommendations = tvShows
                self.personalizedInsights = "Showing trending content. Add more ratings to get personalized recommendations!"
                self.lastUpdateDate = Date()
            }
        } catch {
            print("Error generating fallback recommendations: \(error)")
        }
    }
    
    // MARK: - Helper Methods
    private func extractTitle(from recommendation: String) -> String {
        // Extract title from format "Movie Title (Year) - Description"
        if let titleEnd = recommendation.range(of: " (")?.lowerBound {
            return String(recommendation[..<titleEnd])
        } else if let titleEnd = recommendation.range(of: " -")?.lowerBound {
            return String(recommendation[..<titleEnd])
        }

        // Fallback: take first part before any separator
        let components = recommendation.components(separatedBy: " - ")
        return components.first?.trimmingCharacters(in: .whitespaces) ?? recommendation
    }

    private func extractDescription(from recommendation: String) -> String {
        // Extract description from format "Movie Title (Year) - Description"
        if let descStart = recommendation.range(of: " - ")?.upperBound {
            return String(recommendation[descStart...]).trimmingCharacters(in: .whitespaces)
        }

        // Fallback: return the full recommendation
        return recommendation
    }

    private func calculateConfidence(for recommendation: String, userProfile: UserProfile) -> Double {
        // Simple confidence calculation based on user's watch history
        let totalWatched = userProfile.watchedMovies.count + userProfile.watchedTVShows.count

        if totalWatched == 0 {
            return 0.5 // Low confidence for new users
        } else if totalWatched < 5 {
            return 0.6 // Medium-low confidence
        } else if totalWatched < 15 {
            return 0.8 // Good confidence
        } else {
            return 0.9 // High confidence for users with lots of data
        }
    }
    
}

// MARK: - Supporting Data Structures
struct UserProfile {
    let watchedMovies: [Movie]
    let watchedTVShows: [TVShow]
}

struct MoviePreferences {
    let preferredGenres: [String: Double]
    let averageRating: Double
    let totalWatched: Int
}

struct TVShowPreferences {
    let preferredGenres: [String: Double]
    let averageRating: Double
    let totalWatched: Int
}
