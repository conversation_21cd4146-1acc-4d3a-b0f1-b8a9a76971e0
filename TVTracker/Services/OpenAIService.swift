import Foundation
import Combine

// MARK: - OpenAI Service
class OpenAIService: ObservableObject {
    static let shared = OpenAIService()
    
    private var apiKey: String {
        return ConfigurationManager.shared.openAIAPIKey
    }
    private let baseURL = "https://api.openai.com/v1"
    private let session = URLSession.shared
    
    private init() {}
    
    // MARK: - Recommendation Methods
    func generateMovieRecommendations(userProfile: UserProfile) async throws -> [String] {
        let prompt = buildMovieRecommendationPrompt(userProfile: userProfile)
        let response = try await sendChatCompletion(prompt: prompt)
        return parseRecommendations(from: response)
    }
    
    func generateTVShowRecommendations(userProfile: UserProfile) async throws -> [String] {
        let prompt = buildTVShowRecommendationPrompt(userProfile: userProfile)
        let response = try await sendChatCompletion(prompt: prompt)
        return parseRecommendations(from: response)
    }
    
    func generatePersonalizedInsights(userProfile: UserProfile) async throws -> String {
        let prompt = buildInsightsPrompt(userProfile: userProfile)
        let response = try await sendChatCompletion(prompt: prompt)
        return response.choices.first?.message.content ?? "No insights available"
    }
    
    // MARK: - Core OpenAI API Methods
    private func sendChatCompletion(prompt: String) async throws -> OpenAIChatResponse {
        guard let url = URL(string: "\(baseURL)/chat/completions") else {
            throw OpenAIError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let requestBody = OpenAIChatRequest(
            model: "gpt-4o-mini", // Cost-effective model for recommendations
            messages: [
                OpenAIChatMessage(role: "system", content: "You are an expert movie and TV show recommendation assistant. Provide personalized, accurate recommendations based on user preferences."),
                OpenAIChatMessage(role: "user", content: prompt)
            ],
            maxTokens: 500,
            temperature: 0.7
        )
        
        request.httpBody = try JSONEncoder().encode(requestBody)
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw OpenAIError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            if let errorData = try? JSONDecoder().decode(OpenAIErrorResponse.self, from: data) {
                throw OpenAIError.apiError(errorData.error.message)
            }
            throw OpenAIError.httpError(httpResponse.statusCode)
        }
        
        return try JSONDecoder().decode(OpenAIChatResponse.self, from: data)
    }
    
    // MARK: - Prompt Building
    private func buildMovieRecommendationPrompt(userProfile: UserProfile) -> String {
        var prompt = "Based on the following user's movie watching history, recommend 10 movies they would enjoy:\n\n"
        
        // Add watched movies
        if !userProfile.watchedMovies.isEmpty {
            prompt += "Movies they've watched and rated:\n"
            for movie in userProfile.watchedMovies.prefix(20) {
                let rating = movie.userRating > 0 ? "\(movie.userRating)/10" : "No rating"
                prompt += "- \(movie.title ?? "Unknown") (\(rating))\n"
            }
            prompt += "\n"
        }
        
        // Add preferences analysis
        let preferences = analyzeMoviePreferences(userProfile.watchedMovies)
        if !preferences.preferredGenres.isEmpty {
            prompt += "Their preferred genres based on ratings:\n"
            let sortedGenres = preferences.preferredGenres.sorted { $0.value > $1.value }.prefix(5)
            for (genre, score) in sortedGenres {
                prompt += "- \(genre) (avg rating: \(String(format: "%.1f", score)))\n"
            }
            prompt += "\n"
        }
        
        prompt += """
        Please recommend 10 movies in this exact format:
        1. Movie Title (Year) - Brief reason why they'd like it
        2. Movie Title (Year) - Brief reason why they'd like it
        ...and so on
        
        Focus on movies that match their taste but introduce some variety. Include both popular and hidden gems.
        """
        
        return prompt
    }
    
    private func buildTVShowRecommendationPrompt(userProfile: UserProfile) -> String {
        var prompt = "Based on the following user's TV show watching history, recommend 10 TV shows they would enjoy:\n\n"
        
        // Add watched TV shows
        if !userProfile.watchedTVShows.isEmpty {
            prompt += "TV shows they've watched and rated:\n"
            for tvShow in userProfile.watchedTVShows.prefix(20) {
                let rating = tvShow.userRating > 0 ? "\(tvShow.userRating)/10" : "No rating"
                let status = tvShow.status ?? "Unknown status"
                prompt += "- \(tvShow.name ?? "Unknown") (\(rating)) - \(status)\n"
            }
            prompt += "\n"
        }
        
        // Add preferences analysis
        let preferences = analyzeTVShowPreferences(userProfile.watchedTVShows)
        if !preferences.preferredGenres.isEmpty {
            prompt += "Their preferred genres based on ratings:\n"
            let sortedGenres = preferences.preferredGenres.sorted { $0.value > $1.value }.prefix(5)
            for (genre, score) in sortedGenres {
                prompt += "- \(genre) (avg rating: \(String(format: "%.1f", score)))\n"
            }
            prompt += "\n"
        }
        
        prompt += """
        Please recommend 10 TV shows in this exact format:
        1. TV Show Title (Year) - Brief reason why they'd like it
        2. TV Show Title (Year) - Brief reason why they'd like it
        ...and so on
        
        Consider both completed series and ongoing shows. Mix popular hits with quality hidden gems.
        """
        
        return prompt
    }
    
    private func buildInsightsPrompt(userProfile: UserProfile) -> String {
        var prompt = "Analyze this user's viewing habits and provide personalized insights:\n\n"
        
        // Add viewing statistics
        prompt += "Viewing Statistics:\n"
        prompt += "- Movies watched: \(userProfile.watchedMovies.count)\n"
        prompt += "- TV shows watched: \(userProfile.watchedTVShows.count)\n"
        
        if !userProfile.watchedMovies.isEmpty {
            let avgMovieRating = userProfile.watchedMovies.compactMap { $0.userRating > 0 ? $0.userRating : nil }.reduce(0, +) / Double(userProfile.watchedMovies.count)
            prompt += "- Average movie rating: \(String(format: "%.1f", avgMovieRating))/10\n"
        }
        
        if !userProfile.watchedTVShows.isEmpty {
            let avgTVRating = userProfile.watchedTVShows.compactMap { $0.userRating > 0 ? $0.userRating : nil }.reduce(0, +) / Double(userProfile.watchedTVShows.count)
            prompt += "- Average TV show rating: \(String(format: "%.1f", avgTVRating))/10\n"
        }
        
        prompt += "\n"
        
        // Add recent activity
        let recentMovies = userProfile.watchedMovies.filter { 
            guard let watchedDate = $0.watchedDate else { return false }
            return watchedDate.timeIntervalSinceNow > -30 * 24 * 60 * 60 // Last 30 days
        }
        
        if !recentMovies.isEmpty {
            prompt += "Recent movies (last 30 days):\n"
            for movie in recentMovies.prefix(5) {
                prompt += "- \(movie.title ?? "Unknown")\n"
            }
            prompt += "\n"
        }
        
        prompt += """
        Provide a brief, friendly analysis (2-3 sentences) covering:
        1. Their viewing patterns and preferences
        2. What type of content they seem to enjoy most
        3. A suggestion for exploring new genres or types of content
        
        Keep it conversational and encouraging!
        """
        
        return prompt
    }
    
    // MARK: - Response Parsing
    private func parseRecommendations(from response: OpenAIChatResponse) -> [String] {
        guard let content = response.choices.first?.message.content else {
            return []
        }
        
        // Parse numbered list format
        let lines = content.components(separatedBy: .newlines)
        var recommendations: [String] = []
        
        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespaces)
            // Look for lines starting with numbers (1., 2., etc.)
            if trimmed.range(of: "^\\d+\\.", options: .regularExpression) != nil {
                // Remove the number prefix
                let recommendation = trimmed.replacingOccurrences(of: "^\\d+\\.\\s*", with: "", options: .regularExpression)
                if !recommendation.isEmpty {
                    recommendations.append(recommendation)
                }
            }
        }
        
        return recommendations
    }
    
    // MARK: - Preference Analysis (Helper Methods)
    private func analyzeMoviePreferences(_ movies: [Movie]) -> MoviePreferences {
        var genreScores: [String: Double] = [:]
        var ratingSum: Double = 0
        var ratingCount: Int = 0
        
        for movie in movies {
            // Analyze genres
            if let genres = movie.genres {
                let genreList = genres.components(separatedBy: ",")
                for genre in genreList {
                    let trimmedGenre = genre.trimmingCharacters(in: .whitespaces)
                    genreScores[trimmedGenre, default: 0] += movie.userRating
                }
            }
            
            // Calculate average rating
            if movie.userRating > 0 {
                ratingSum += movie.userRating
                ratingCount += 1
            }
        }
        
        let averageRating = ratingCount > 0 ? ratingSum / Double(ratingCount) : 7.0
        
        return MoviePreferences(
            preferredGenres: genreScores,
            averageRating: averageRating,
            totalWatched: movies.count
        )
    }
    
    private func analyzeTVShowPreferences(_ tvShows: [TVShow]) -> TVShowPreferences {
        var genreScores: [String: Double] = [:]
        var ratingSum: Double = 0
        var ratingCount: Int = 0
        
        for tvShow in tvShows {
            // Analyze genres
            if let genres = tvShow.genres {
                let genreList = genres.components(separatedBy: ",")
                for genre in genreList {
                    let trimmedGenre = genre.trimmingCharacters(in: .whitespaces)
                    genreScores[trimmedGenre, default: 0] += tvShow.userRating
                }
            }
            
            // Calculate average rating
            if tvShow.userRating > 0 {
                ratingSum += tvShow.userRating
                ratingCount += 1
            }
        }
        
        let averageRating = ratingCount > 0 ? ratingSum / Double(ratingCount) : 7.0
        
        return TVShowPreferences(
            preferredGenres: genreScores,
            averageRating: averageRating,
            totalWatched: tvShows.count
        )
    }
}

// MARK: - OpenAI Data Models
struct OpenAIChatRequest: Codable {
    let model: String
    let messages: [OpenAIChatMessage]
    let maxTokens: Int
    let temperature: Double
    
    enum CodingKeys: String, CodingKey {
        case model, messages, temperature
        case maxTokens = "max_tokens"
    }
}

struct OpenAIChatMessage: Codable {
    let role: String
    let content: String
}

struct OpenAIChatResponse: Codable {
    let choices: [OpenAIChatChoice]
    let usage: OpenAIUsage?
}

struct OpenAIChatChoice: Codable {
    let message: OpenAIChatMessage
    let finishReason: String?
    
    enum CodingKeys: String, CodingKey {
        case message
        case finishReason = "finish_reason"
    }
}

struct OpenAIUsage: Codable {
    let promptTokens: Int
    let completionTokens: Int
    let totalTokens: Int
    
    enum CodingKeys: String, CodingKey {
        case promptTokens = "prompt_tokens"
        case completionTokens = "completion_tokens"
        case totalTokens = "total_tokens"
    }
}

struct OpenAIErrorResponse: Codable {
    let error: OpenAIErrorDetail
}

struct OpenAIErrorDetail: Codable {
    let message: String
    let type: String?
    let code: String?
}

// MARK: - OpenAI Errors
enum OpenAIError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case apiError(String)
    case httpError(Int)
    case noAPIKey
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid OpenAI API URL"
        case .invalidResponse:
            return "Invalid response from OpenAI API"
        case .apiError(let message):
            return "OpenAI API Error: \(message)"
        case .httpError(let code):
            return "HTTP Error: \(code)"
        case .noAPIKey:
            return "OpenAI API key not configured"
        }
    }
}
