import SwiftUI

struct AIRecommendationsView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var recommendationEngine = RecommendationEngine.shared
    @State private var selectedTab = 0
    @State private var showingSettings = false
    @State private var refreshing = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with AI branding
                aiHeader
                
                // Tab selector
                tabSelector
                
                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    // AI Movie Recommendations
                    movieRecommendationsView
                        .tag(0)
                    
                    // AI TV Show Recommendations
                    tvShowRecommendationsView
                        .tag(1)
                    
                    // Personalized Insights
                    insightsView
                        .tag(2)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("AI Recommendations")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingSettings = true }) {
                        Image(systemName: "gear")
                    }
                }
            }
            .refreshable {
                await refreshRecommendations()
            }
            .onAppear {
                if recommendationEngine.movieRecommendations.isEmpty {
                    Task {
                        await recommendationEngine.generateRecommendations(for: viewContext)
                    }
                }
            }
            .sheet(isPresented: $showingSettings) {
                AISettingsView()
            }
        }
    }
    
    // MARK: - AI Header
    private var aiHeader: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "brain.head.profile")
                    .font(.title2)
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("AI-Powered Recommendations")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("Personalized suggestions based on your viewing history")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if recommendationEngine.isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 12)
            
            if let lastUpdate = recommendationEngine.lastUpdateDate {
                HStack {
                    Image(systemName: "clock")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Text("Updated \(lastUpdate, style: .relative) ago")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
                .padding(.horizontal)
                .padding(.bottom, 8)
            }
        }
        .background(Color(.systemGray6))
    }
    
    // MARK: - Tab Selector
    private var tabSelector: some View {
        HStack(spacing: 0) {
            TabButton(title: "Movies", isSelected: selectedTab == 0) {
                withAnimation(.easeInOut(duration: 0.2)) {
                    selectedTab = 0
                }
            }
            
            TabButton(title: "TV Shows", isSelected: selectedTab == 1) {
                withAnimation(.easeInOut(duration: 0.2)) {
                    selectedTab = 1
                }
            }
            
            TabButton(title: "Insights", isSelected: selectedTab == 2) {
                withAnimation(.easeInOut(duration: 0.2)) {
                    selectedTab = 2
                }
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Movie Recommendations View
    private var movieRecommendationsView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if recommendationEngine.movieRecommendations.isEmpty && !recommendationEngine.isLoading {
                    emptyStateView(type: "movies")
                } else {
                    ForEach(recommendationEngine.movieRecommendations, id: \.id) { recommendation in
                        AIRecommendationCard(recommendation: recommendation)
                            .slideInAnimation(delay: Double(recommendationEngine.movieRecommendations.firstIndex(where: { $0.id == recommendation.id }) ?? 0) * 0.1)
                    }
                }
            }
            .padding()
        }
    }
    
    // MARK: - TV Show Recommendations View
    private var tvShowRecommendationsView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if recommendationEngine.tvShowRecommendations.isEmpty && !recommendationEngine.isLoading {
                    emptyStateView(type: "TV shows")
                } else {
                    ForEach(recommendationEngine.tvShowRecommendations, id: \.id) { recommendation in
                        AIRecommendationCard(recommendation: recommendation)
                            .slideInAnimation(delay: Double(recommendationEngine.tvShowRecommendations.firstIndex(where: { $0.id == recommendation.id }) ?? 0) * 0.1)
                    }
                }
            }
            .padding()
        }
    }
    
    // MARK: - Insights View
    private var insightsView: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Personalized Insights Card
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "lightbulb.fill")
                            .foregroundColor(.yellow)
                        
                        Text("Your Viewing Insights")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    
                    if recommendationEngine.personalizedInsights.isEmpty && !recommendationEngine.isLoading {
                        Text("Watch and rate more content to get personalized insights!")
                            .font(.body)
                            .foregroundColor(.secondary)
                    } else {
                        Text(recommendationEngine.personalizedInsights)
                            .font(.body)
                            .lineLimit(nil)
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )
                
                // Statistics Cards
                statisticsView
                
                // Tips for Better Recommendations
                tipsView
            }
            .padding()
        }
    }
    
    // MARK: - Statistics View
    private var statisticsView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recommendation Statistics")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 16) {
                StatCard(
                    title: "Movies",
                    value: "\(recommendationEngine.movieRecommendations.count)",
                    icon: "film",
                    color: .blue
                )
                
                StatCard(
                    title: "TV Shows",
                    value: "\(recommendationEngine.tvShowRecommendations.count)",
                    icon: "tv",
                    color: .purple
                )
                
                StatCard(
                    title: "AI Generated",
                    value: "\(recommendationEngine.movieRecommendations.filter(\.aiGenerated).count + recommendationEngine.tvShowRecommendations.filter(\.aiGenerated).count)",
                    icon: "brain.head.profile",
                    color: .green
                )
            }
        }
    }
    
    // MARK: - Tips View
    private var tipsView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Tips for Better Recommendations")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(alignment: .leading, spacing: 8) {
                TipRow(icon: "star.fill", text: "Rate more content to improve accuracy")
                TipRow(icon: "heart.fill", text: "Mark content as watched to build your profile")
                TipRow(icon: "arrow.clockwise", text: "Refresh recommendations weekly")
                TipRow(icon: "gear", text: "Adjust AI settings for personalized results")
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
    
    // MARK: - Helper Views
    private func emptyStateView(type: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 50))
                .foregroundColor(.gray)
            
            Text("No AI Recommendations Yet")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Watch and rate some \(type) to get personalized AI recommendations!")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 50)
    }
    
    // MARK: - Helper Methods
    private func refreshRecommendations() async {
        refreshing = true
        await recommendationEngine.generateRecommendations(for: viewContext)
        refreshing = false
    }
}

// MARK: - Supporting Views
struct TabButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(isSelected ? .semibold : .regular)
                    .foregroundColor(isSelected ? .blue : .secondary)
                
                Rectangle()
                    .fill(isSelected ? Color.blue : Color.clear)
                    .frame(height: 2)
            }
        }
        .frame(maxWidth: .infinity)
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6))
        )
    }
}

struct TipRow: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 20)
            
            Text(text)
                .font(.subheadline)
            
            Spacer()
        }
    }
}

// MARK: - AI Settings View
struct AISettingsView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("AI Settings")
                    .font(.title)
                    .padding()
                
                Text("Settings for AI recommendations will be implemented here")
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            .navigationTitle("AI Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    AIRecommendationsView()
}
