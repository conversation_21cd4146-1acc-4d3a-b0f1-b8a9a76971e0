import SwiftUI

struct EnhancedSearchView: View {
    @StateObject private var tmdbService = TMDBService.shared
    @State private var searchText = ""
    @State private var searchResults: [TMDBSearchResult] = []
    @State private var isSearching = false
    @State private var selectedFilter: SearchFilter = .all
    @State private var showingFilters = false
    
    private let searchDebouncer = Debouncer(delay: 0.5)
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search header
                searchHeader
                
                // Filter chips
                if !searchText.isEmpty {
                    filterChips
                        .slideInAnimation(delay: 0.1)
                }
                
                // Search results
                searchContent
            }
            .navigationTitle("Search")
            .navigationBarTitleDisplayMode(.large)
            .searchable(text: $searchText, prompt: "Search movies and TV shows")
            .onChange(of: searchText) { newValue in
                searchDebouncer.debounce {
                    performSearch(query: newValue)
                }
            }
            .accessibilityLabel("Search for movies and TV shows")
        }
    }
    
    // MARK: - Search Header
    private var searchHeader: some View {
        VStack(spacing: 16) {
            // Search suggestions when empty
            if searchText.isEmpty && searchResults.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "magnifyingglass")
                        .font(.system(size: 40))
                        .foregroundColor(.secondary)
                        .bounceAnimation()
                    
                    Text("Discover Movies & TV Shows")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("Search for your favorite content or explore trending titles")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .padding(.top, 40)
            }
        }
    }
    
    // MARK: - Filter Chips
    private var filterChips: View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(SearchFilter.allCases, id: \.self) { filter in
                    FilterChip(
                        title: filter.displayName,
                        isSelected: selectedFilter == filter,
                        action: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                selectedFilter = filter
                            }
                            filterResults()
                        }
                    )
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Search Content
    private var searchContent: some View {
        Group {
            if isSearching {
                loadingView
            } else if searchResults.isEmpty && !searchText.isEmpty {
                emptyStateView
            } else if !searchResults.isEmpty {
                resultsView
            } else {
                trendingView
            }
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Searching...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .accessibilityLabel("Searching for content")
    }
    
    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "questionmark.circle")
                .font(.system(size: 40))
                .foregroundColor(.secondary)
            
            Text("No Results Found")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Try searching with different keywords or check your spelling")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .accessibilityLabel("No search results found")
    }
    
    // MARK: - Results View
    private var resultsView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(filteredResults, id: \.id) { result in
                    SearchResultRow(result: result)
                        .slideInAnimation(delay: Double(filteredResults.firstIndex(where: { $0.id == result.id }) ?? 0) * 0.05)
                }
            }
            .padding()
        }
    }
    
    // MARK: - Trending View
    private var trendingView: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Trending movies section
                MediaCarouselSection(
                    title: "Trending Movies",
                    subtitle: "Popular movies this week"
                ) {
                    ForEach(0..<10, id: \.self) { _ in
                        AnimatedMediaCard(
                            title: "Trending Movie",
                            subtitle: "2024",
                            imageURL: nil,
                            progress: nil,
                            isWatched: false,
                            onTap: {}
                        )
                    }
                }
                
                // Trending TV shows section
                MediaCarouselSection(
                    title: "Trending TV Shows",
                    subtitle: "Popular shows this week"
                ) {
                    ForEach(0..<10, id: \.self) { _ in
                        AnimatedMediaCard(
                            title: "Trending Show",
                            subtitle: "2024",
                            imageURL: nil,
                            progress: nil,
                            isWatched: false,
                            onTap: {}
                        )
                    }
                }
            }
            .padding(.top)
        }
    }
    
    // MARK: - Computed Properties
    private var filteredResults: [TMDBSearchResult] {
        switch selectedFilter {
        case .all:
            return searchResults
        case .movies:
            return searchResults.filter { $0.mediaType == "movie" }
        case .tvShows:
            return searchResults.filter { $0.mediaType == "tv" }
        case .people:
            return searchResults.filter { $0.mediaType == "person" }
        }
    }
    
    // MARK: - Helper Methods
    private func performSearch(query: String) {
        guard !query.trimmingCharacters(in: .whitespaces).isEmpty else {
            searchResults = []
            return
        }
        
        isSearching = true
        
        Task {
            do {
                let results = try await tmdbService.searchMulti(query: query)
                await MainActor.run {
                    self.searchResults = results
                    self.isSearching = false
                }
            } catch {
                await MainActor.run {
                    self.searchResults = []
                    self.isSearching = false
                }
                print("Search error: \(error)")
            }
        }
    }
    
    private func filterResults() {
        // Results are filtered in the computed property
    }
}

// MARK: - Filter Chip
struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? Color.blue : Color.gray.opacity(0.2))
                )
                .foregroundColor(isSelected ? .white : .primary)
        }
        .accessibilityLabel("\(title) filter")
        .accessibilityHint(isSelected ? "Currently selected" : "Tap to filter by \(title)")
    }
}

// MARK: - Search Result Row
struct SearchResultRow: View {
    let result: TMDBSearchResult
    
    var body: some View {
        HStack(spacing: 12) {
            // Poster image
            AsyncImage(url: TMDBService.shared.posterURL(for: result.posterPath, size: .w185)) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.3))
                    .overlay(
                        Image(systemName: mediaTypeIcon)
                            .foregroundColor(.gray)
                    )
            }
            .frame(width: 60, height: 90)
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // Content info
            VStack(alignment: .leading, spacing: 4) {
                Text(result.displayTitle)
                    .font(.headline)
                    .fontWeight(.medium)
                    .lineLimit(2)
                
                if let overview = result.overview, !overview.isEmpty {
                    Text(overview)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(3)
                }
                
                HStack {
                    // Media type badge
                    Text(result.mediaType.capitalized)
                        .font(.caption2)
                        .fontWeight(.medium)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color.blue.opacity(0.2))
                        )
                        .foregroundColor(.blue)
                    
                    // Rating
                    if result.voteAverage > 0 {
                        HStack(spacing: 2) {
                            Image(systemName: "star.fill")
                                .font(.caption2)
                                .foregroundColor(.yellow)
                            
                            Text(String(format: "%.1f", result.voteAverage))
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Spacer()
                }
            }
            
            Spacer()
            
            // Action button
            Button(action: {}) {
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(result.displayTitle), \(result.mediaType)")
        .accessibilityHint("Tap to view details")
    }
    
    private var mediaTypeIcon: String {
        switch result.mediaType {
        case "movie":
            return "film"
        case "tv":
            return "tv"
        case "person":
            return "person"
        default:
            return "photo"
        }
    }
}

// MARK: - Search Filter Enum
enum SearchFilter: CaseIterable {
    case all, movies, tvShows, people
    
    var displayName: String {
        switch self {
        case .all: return "All"
        case .movies: return "Movies"
        case .tvShows: return "TV Shows"
        case .people: return "People"
        }
    }
}

// MARK: - Debouncer
class Debouncer {
    private let delay: TimeInterval
    private var workItem: DispatchWorkItem?
    
    init(delay: TimeInterval) {
        self.delay = delay
    }
    
    func debounce(action: @escaping () -> Void) {
        workItem?.cancel()
        workItem = DispatchWorkItem(block: action)
        DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: workItem!)
    }
}

#Preview {
    EnhancedSearchView()
}
