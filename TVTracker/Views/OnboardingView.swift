import SwiftUI

// MARK: - Onboarding View
struct OnboardingView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var onboardingManager = OnboardingManager()
    @State private var currentStep = 0
    @Binding var isOnboardingComplete: Bool
    
    var body: some View {
        ZStack {
            // Background gradient
            LinearGradient(
                colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack {
                if currentStep == 0 {
                    welcomeView
                } else if currentStep == 1 {
                    SwipeCardsView(
                        onboardingManager: onboardingManager,
                        onComplete: {
                            currentStep = 2
                        }
                    )
                } else {
                    completionView
                }
            }
        }
    }
    
    // MARK: - Welcome View
    private var welcomeView: some View {
        VStack(spacing: 30) {
            Spacer()
            
            // App Icon and Title
            VStack(spacing: 16) {
                Image(systemName: "tv.and.hifispeaker.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.blue)
                    .bounceAnimation()
                
                Text("Welcome to TVTracker")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                Text("Your personal movie and TV show companion")
                    .font(.title3)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
            
            // Features
            VStack(spacing: 20) {
                FeatureRow(
                    icon: "brain.head.profile",
                    title: "AI Recommendations",
                    description: "Get personalized suggestions powered by AI"
                )
                
                FeatureRow(
                    icon: "icloud.fill",
                    title: "iCloud Sync",
                    description: "Your data syncs across all your devices"
                )
                
                FeatureRow(
                    icon: "bell.fill",
                    title: "Smart Notifications",
                    description: "Never miss new episodes or releases"
                )
            }
            
            Spacer()
            
            // Get Started Button
            Button(action: {
                withAnimation(.easeInOut(duration: 0.5)) {
                    currentStep = 1
                }
            }) {
                Text("Get Started")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.blue)
                    )
            }
            .padding(.horizontal)
        }
        .padding()
    }
    
    // MARK: - Completion View
    private var completionView: some View {
        VStack(spacing: 30) {
            Spacer()
            
            // Success Animation
            VStack(spacing: 16) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.green)
                    .scaleEffect(1.2)
                    .animation(.easeInOut(duration: 0.6).repeatCount(1), value: currentStep)
                
                Text("You're All Set!")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("We've learned about your preferences and will start generating personalized recommendations.")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
            
            // Stats
            VStack(spacing: 12) {
                Text("Your Onboarding Stats")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                HStack(spacing: 40) {
                    StatItem(
                        value: "\(onboardingManager.likedItems.count)",
                        label: "Liked",
                        color: .green
                    )
                    
                    StatItem(
                        value: "\(onboardingManager.watchedItems.count)",
                        label: "Watched",
                        color: .blue
                    )
                    
                    StatItem(
                        value: "\(onboardingManager.dislikedItems.count)",
                        label: "Passed",
                        color: .orange
                    )
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
            
            Spacer()
            
            // Continue Button
            Button(action: {
                completeOnboarding()
            }) {
                Text("Start Using TVTracker")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.blue)
                    )
            }
            .padding(.horizontal)
        }
        .padding()
    }
    
    private func completeOnboarding() {
        // Save onboarding data to Core Data
        onboardingManager.saveToDatabase(context: viewContext)
        
        // Mark onboarding as complete
        UserDefaults.standard.set(true, forKey: "onboardingComplete")
        
        withAnimation(.easeInOut(duration: 0.5)) {
            isOnboardingComplete = true
        }
    }
}

// MARK: - Feature Row
struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.horizontal)
    }
}

// MARK: - Stat Item
struct StatItem: View {
    let value: String
    let label: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Swipe Cards View
struct SwipeCardsView: View {
    @ObservedObject var onboardingManager: OnboardingManager
    let onComplete: () -> Void
    
    @State private var currentIndex = 0
    @State private var dragOffset = CGSize.zero
    @State private var isAnimating = false
    
    var body: some View {
        VStack(spacing: 20) {
            // Header
            VStack(spacing: 8) {
                Text("Build Your Profile")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("Swipe to tell us about your preferences")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                // Progress
                ProgressView(value: Double(currentIndex), total: Double(onboardingManager.sampleContent.count))
                    .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                    .scaleEffect(x: 1, y: 2)
                    .padding(.horizontal)
            }
            .padding()
            
            // Swipe Instructions
            HStack(spacing: 40) {
                SwipeInstruction(
                    icon: "hand.thumbsdown.fill",
                    text: "Swipe Left\nNot Interested",
                    color: .red
                )
                
                SwipeInstruction(
                    icon: "hand.thumbsup.fill",
                    text: "Swipe Right\nLike It",
                    color: .green
                )
                
                SwipeInstruction(
                    icon: "arrow.up.circle.fill",
                    text: "Swipe Up\nAlready Watched",
                    color: .blue
                )
            }
            .padding(.horizontal)
            
            // Card Stack
            ZStack {
                ForEach(Array(onboardingManager.sampleContent.enumerated().reversed()), id: \.element.id) { index, item in
                    if index >= currentIndex && index < currentIndex + 3 {
                        SwipeCard(
                            item: item,
                            isTopCard: index == currentIndex,
                            offset: index == currentIndex ? dragOffset : .zero,
                            onSwipe: { direction in
                                handleSwipe(direction: direction, item: item)
                            }
                        )
                        .scaleEffect(index == currentIndex ? 1.0 : 0.95 - Double(index - currentIndex) * 0.05)
                        .offset(y: CGFloat(index - currentIndex) * 10)
                        .zIndex(Double(onboardingManager.sampleContent.count - index))
                        .gesture(
                            index == currentIndex ? DragGesture()
                                .onChanged { value in
                                    if !isAnimating {
                                        dragOffset = value.translation
                                    }
                                }
                                .onEnded { value in
                                    if !isAnimating {
                                        handleDragEnd(value: value, item: item)
                                    }
                                }
                            : nil
                        )
                    }
                }
            }
            .frame(height: 400)
            
            Spacer()
            
            // Skip Button
            Button("Skip Setup") {
                onComplete()
            }
            .foregroundColor(.secondary)
        }
    }
    
    private func handleSwipe(direction: SwipeDirection, item: OnboardingContentItem) {
        guard !isAnimating else { return }
        
        isAnimating = true
        
        withAnimation(.easeInOut(duration: 0.3)) {
            switch direction {
            case .left:
                dragOffset = CGSize(width: -500, height: 0)
            case .right:
                dragOffset = CGSize(width: 500, height: 0)
            case .up:
                dragOffset = CGSize(width: 0, height: -500)
            }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            onboardingManager.handleSwipe(direction: direction, item: item)
            nextCard()
        }
    }
    
    private func handleDragEnd(value: DragGesture.Value, item: OnboardingContentItem) {
        let threshold: CGFloat = 100
        
        if abs(value.translation.x) > threshold {
            let direction: SwipeDirection = value.translation.x > 0 ? .right : .left
            handleSwipe(direction: direction, item: item)
        } else if value.translation.y < -threshold {
            handleSwipe(direction: .up, item: item)
        } else {
            withAnimation(.spring()) {
                dragOffset = .zero
            }
        }
    }
    
    private func nextCard() {
        currentIndex += 1
        dragOffset = .zero
        isAnimating = false
        
        if currentIndex >= onboardingManager.sampleContent.count {
            onComplete()
        }
    }
}

// MARK: - Swipe Instruction
struct SwipeInstruction: View {
    let icon: String
    let text: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(text)
                .font(.caption)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
        }
    }
}

#Preview {
    OnboardingView(isOnboardingComplete: .constant(false))
}
