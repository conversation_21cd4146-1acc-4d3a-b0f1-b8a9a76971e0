import SwiftUI

struct AIRecommendationCard: View {
    let recommendation: RecommendationItem
    @State private var isExpanded = false
    @State private var showingDetail = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Main card content
            HStack(alignment: .top, spacing: 12) {
                // Poster placeholder or image
                posterView
                
                // Content info
                VStack(alignment: .leading, spacing: 8) {
                    // Title and year
                    titleSection
                    
                    // AI badge and confidence
                    badgeSection
                    
                    // Description
                    descriptionSection
                    
                    // Action buttons
                    actionButtons
                }
                
                Spacer()
            }
            .padding()
            .background(Color(.systemBackground))
            
            // Expanded details (if expanded)
            if isExpanded {
                expandedDetails
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.3)) {
                isExpanded.toggle()
            }
        }
    }
    
    // MARK: - Poster View
    private var posterView: some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(Color.gray.opacity(0.3))
            .frame(width: 60, height: 90)
            .overlay(
                Group {
                    if let posterURL = recommendation.posterURL() {
                        AsyncImage(url: posterURL) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            ProgressView()
                                .scaleEffect(0.6)
                        }
                    } else {
                        Image(systemName: recommendation.typeIcon)
                            .font(.title2)
                            .foregroundColor(.gray)
                    }
                }
            )
            .clipShape(RoundedRectangle(cornerRadius: 8))
    }
    
    // MARK: - Title Section
    private var titleSection: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(recommendation.title)
                .font(.headline)
                .fontWeight(.semibold)
                .lineLimit(2)
            
            if let year = recommendation.releaseYear {
                Text(year)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - Badge Section
    private var badgeSection: some View {
        HStack(spacing: 8) {
            // AI Generated Badge
            if recommendation.aiGenerated {
                HStack(spacing: 4) {
                    Image(systemName: "brain.head.profile")
                        .font(.caption2)
                    Text("AI")
                        .font(.caption2)
                        .fontWeight(.medium)
                }
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.blue.opacity(0.2))
                )
                .foregroundColor(.blue)
            }
            
            // Confidence Badge
            HStack(spacing: 4) {
                Image(systemName: "target")
                    .font(.caption2)
                Text(recommendation.confidenceText)
                    .font(.caption2)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(
                RoundedRectangle(cornerRadius: 4)
                    .fill(confidenceColor.opacity(0.2))
            )
            .foregroundColor(confidenceColor)
            
            Spacer()
        }
    }
    
    // MARK: - Description Section
    private var descriptionSection: some View {
        Text(recommendation.description)
            .font(.subheadline)
            .foregroundColor(.secondary)
            .lineLimit(isExpanded ? nil : 3)
            .animation(.easeInOut(duration: 0.2), value: isExpanded)
    }
    
    // MARK: - Action Buttons
    private var actionButtons: some View {
        HStack(spacing: 12) {
            // Add to Watchlist Button
            Button(action: addToWatchlist) {
                HStack(spacing: 4) {
                    Image(systemName: "plus.circle")
                        .font(.caption)
                    Text("Add")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.blue.opacity(0.1))
                )
                .foregroundColor(.blue)
            }
            
            // View Details Button
            Button(action: { showingDetail = true }) {
                HStack(spacing: 4) {
                    Image(systemName: "info.circle")
                        .font(.caption)
                    Text("Details")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
                .foregroundColor(.primary)
            }
            
            Spacer()
            
            // Expand/Collapse Button
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded.toggle()
                }
            }) {
                Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - Expanded Details
    private var expandedDetails: some View {
        VStack(alignment: .leading, spacing: 12) {
            Divider()
            
            // Additional info
            VStack(alignment: .leading, spacing: 8) {
                if !recommendation.genres.isEmpty {
                    HStack {
                        Text("Genres:")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                        
                        Text(recommendation.genres.joined(separator: ", "))
                            .font(.caption)
                            .foregroundColor(.primary)
                    }
                }
                
                if let rating = recommendation.rating {
                    HStack {
                        Text("Rating:")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                        
                        HStack(spacing: 2) {
                            Image(systemName: "star.fill")
                                .font(.caption2)
                                .foregroundColor(.yellow)
                            
                            Text(String(format: "%.1f", rating))
                                .font(.caption)
                                .foregroundColor(.primary)
                        }
                    }
                }
                
                // Why recommended
                if recommendation.aiGenerated {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Why recommended:")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                        
                        Text("Based on your viewing history and preferences, this matches your taste for \(recommendation.type.displayName.lowercased()) content.")
                            .font(.caption)
                            .foregroundColor(.primary)
                            .italic()
                    }
                }
            }
            
            // Enhanced action buttons
            HStack(spacing: 12) {
                Button(action: markAsWatched) {
                    HStack(spacing: 4) {
                        Image(systemName: "checkmark.circle")
                            .font(.caption)
                        Text("Mark Watched")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.green.opacity(0.1))
                    )
                    .foregroundColor(.green)
                }
                
                Button(action: notInterested) {
                    HStack(spacing: 4) {
                        Image(systemName: "hand.thumbsdown")
                            .font(.caption)
                        Text("Not Interested")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.red.opacity(0.1))
                    )
                    .foregroundColor(.red)
                }
                
                Spacer()
            }
        }
        .padding(.horizontal)
        .padding(.bottom)
    }
    
    // MARK: - Computed Properties
    private var confidenceColor: Color {
        switch recommendation.confidence {
        case 0.8...1.0:
            return .green
        case 0.6..<0.8:
            return .orange
        default:
            return .red
        }
    }
    
    // MARK: - Action Methods
    private func addToWatchlist() {
        // TODO: Implement add to watchlist functionality
        print("Adding \(recommendation.title) to watchlist")
    }
    
    private func markAsWatched() {
        // TODO: Implement mark as watched functionality
        print("Marking \(recommendation.title) as watched")
    }
    
    private func notInterested() {
        // TODO: Implement not interested functionality
        print("User not interested in \(recommendation.title)")
    }
}

// MARK: - Compact AI Recommendation Card
struct CompactAIRecommendationCard: View {
    let recommendation: RecommendationItem
    
    var body: some View {
        HStack(spacing: 12) {
            // Poster
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.gray.opacity(0.3))
                .frame(width: 40, height: 60)
                .overlay(
                    Group {
                        if let posterURL = recommendation.posterURL() {
                            AsyncImage(url: posterURL) { image in
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                            } placeholder: {
                                ProgressView()
                                    .scaleEffect(0.5)
                            }
                        } else {
                            Image(systemName: recommendation.typeIcon)
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    }
                )
                .clipShape(RoundedRectangle(cornerRadius: 6))
            
            // Content
            VStack(alignment: .leading, spacing: 4) {
                Text(recommendation.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Text(recommendation.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                HStack {
                    if recommendation.aiGenerated {
                        HStack(spacing: 2) {
                            Image(systemName: "brain.head.profile")
                                .font(.caption2)
                            Text("AI")
                                .font(.caption2)
                        }
                        .foregroundColor(.blue)
                    }
                    
                    Text(recommendation.confidenceText)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
            }
            
            Spacer()
            
            // Action button
            Button(action: {}) {
                Image(systemName: "plus.circle")
                    .foregroundColor(.blue)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6))
        )
    }
}

#Preview {
    VStack(spacing: 16) {
        AIRecommendationCard(
            recommendation: RecommendationItem(
                id: 1,
                title: "Inception",
                description: "A mind-bending thriller about dreams within dreams. Perfect for fans of complex narratives and Christopher Nolan's directing style.",
                type: .movie,
                aiGenerated: true,
                confidence: 0.92,
                releaseYear: "2010",
                genres: ["Sci-Fi", "Thriller", "Action"],
                rating: 8.8
            )
        )
        
        CompactAIRecommendationCard(
            recommendation: RecommendationItem(
                id: 2,
                title: "Breaking Bad",
                description: "A chemistry teacher turned meth cook. Intense drama with complex characters.",
                type: .tvShow,
                aiGenerated: true,
                confidence: 0.85
            )
        )
    }
    .padding()
}
