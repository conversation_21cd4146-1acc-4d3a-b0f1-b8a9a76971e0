import SwiftUI

// MARK: - Swipe Card
struct SwipeCard: View {
    let item: OnboardingContentItem
    let isTopCard: Bool
    let offset: CGSize
    let onSwipe: (SwipeDirection) -> Void
    
    @State private var rotation: Double = 0
    
    var body: some View {
        ZStack {
            // Card Background
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
            
            // Content
            VStack(spacing: 0) {
                // Poster Image
                AsyncImage(url: item.posterURL) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .overlay(
                            VStack {
                                Image(systemName: item.type == .movie ? "film" : "tv")
                                    .font(.system(size: 40))
                                    .foregroundColor(.gray)
                                
                                Text("Loading...")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        )
                }
                .frame(height: 250)
                .clipped()
                
                // Content Info
                VStack(alignment: .leading, spacing: 12) {
                    // Title and Year
                    VStack(alignment: .leading, spacing: 4) {
                        Text(item.title)
                            .font(.title2)
                            .fontWeight(.bold)
                            .lineLimit(2)
                        
                        HStack {
                            if let year = item.year {
                                Text(year)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            // Rating
                            HStack(spacing: 4) {
                                Image(systemName: "star.fill")
                                    .font(.caption)
                                    .foregroundColor(.yellow)
                                
                                Text(String(format: "%.1f", item.rating))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    
                    // Genres
                    if !item.genres.isEmpty {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 8) {
                                ForEach(item.genres.prefix(3), id: \.self) { genre in
                                    Text(genre)
                                        .font(.caption)
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 4)
                                        .background(
                                            RoundedRectangle(cornerRadius: 12)
                                                .fill(Color.blue.opacity(0.1))
                                        )
                                        .foregroundColor(.blue)
                                }
                            }
                            .padding(.horizontal, 1)
                        }
                    }
                    
                    // Overview
                    Text(item.overview)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(3)
                        .multilineTextAlignment(.leading)
                }
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            
            // Swipe Indicators
            if isTopCard {
                swipeIndicators
            }
        }
        .frame(width: 300, height: 400)
        .rotationEffect(.degrees(rotation))
        .offset(offset)
        .onChange(of: offset) { newOffset in
            withAnimation(.easeOut(duration: 0.1)) {
                rotation = Double(newOffset.x / 20)
            }
        }
    }
    
    // MARK: - Swipe Indicators
    private var swipeIndicators: some View {
        ZStack {
            // Left swipe (dislike)
            if offset.x < -50 {
                VStack {
                    HStack {
                        Spacer()
                        
                        VStack {
                            Image(systemName: "hand.thumbsdown.fill")
                                .font(.title)
                                .foregroundColor(.white)
                            
                            Text("PASS")
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.red)
                        )
                        .rotationEffect(.degrees(15))
                        .scaleEffect(min(abs(offset.x) / 100, 1.0))
                        
                        Spacer()
                    }
                    
                    Spacer()
                }
                .padding(.top, 20)
            }
            
            // Right swipe (like)
            if offset.x > 50 {
                VStack {
                    HStack {
                        Spacer()
                        
                        VStack {
                            Image(systemName: "hand.thumbsup.fill")
                                .font(.title)
                                .foregroundColor(.white)
                            
                            Text("LIKE")
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.green)
                        )
                        .rotationEffect(.degrees(-15))
                        .scaleEffect(min(offset.x / 100, 1.0))
                        
                        Spacer()
                    }
                    
                    Spacer()
                }
                .padding(.top, 20)
            }
            
            // Up swipe (watched)
            if offset.y < -50 {
                VStack {
                    HStack {
                        Spacer()
                        
                        VStack {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.title)
                                .foregroundColor(.white)
                            
                            Text("WATCHED")
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.blue)
                        )
                        .scaleEffect(min(abs(offset.y) / 100, 1.0))
                        
                        Spacer()
                    }
                    
                    Spacer()
                }
                .padding(.top, 20)
            }
        }
    }
}

// MARK: - Swipe Direction
enum SwipeDirection {
    case left, right, up
}

// MARK: - Onboarding Content Item
struct OnboardingContentItem: Identifiable {
    let id: Int
    let title: String
    let overview: String
    let posterURL: URL?
    let year: String?
    let rating: Double
    let genres: [String]
    let type: ContentType
    
    enum ContentType {
        case movie, tvShow
    }
}

// MARK: - Onboarding Manager
class OnboardingManager: ObservableObject {
    @Published var sampleContent: [OnboardingContentItem] = []
    @Published var likedItems: [OnboardingContentItem] = []
    @Published var watchedItems: [OnboardingContentItem] = []
    @Published var dislikedItems: [OnboardingContentItem] = []
    
    private let tmdbService = TMDBService.shared
    
    init() {
        loadSampleContent()
    }
    
    func handleSwipe(direction: SwipeDirection, item: OnboardingContentItem) {
        switch direction {
        case .left:
            dislikedItems.append(item)
        case .right:
            likedItems.append(item)
        case .up:
            watchedItems.append(item)
        }
    }
    
    func saveToDatabase(context: NSManagedObjectContext) {
        // Save liked items to watchlist
        for item in likedItems {
            if item.type == .movie {
                let movie = Movie(context: context)
                movie.id = Int32(item.id)
                movie.title = item.title
                movie.overview = item.overview
                movie.posterPath = item.posterURL?.lastPathComponent
                movie.voteAverage = item.rating
                movie.isWatched = false
                movie.userRating = 0
            } else {
                let tvShow = TVShow(context: context)
                tvShow.id = Int32(item.id)
                tvShow.name = item.title
                tvShow.overview = item.overview
                tvShow.posterPath = item.posterURL?.lastPathComponent
                tvShow.voteAverage = item.rating
                tvShow.isWatched = false
                tvShow.userRating = 0
            }
        }
        
        // Save watched items
        for item in watchedItems {
            if item.type == .movie {
                let movie = Movie(context: context)
                movie.id = Int32(item.id)
                movie.title = item.title
                movie.overview = item.overview
                movie.posterPath = item.posterURL?.lastPathComponent
                movie.voteAverage = item.rating
                movie.isWatched = true
                movie.watchedDate = Date()
                movie.userRating = item.rating // Use TMDB rating as initial user rating
            } else {
                let tvShow = TVShow(context: context)
                tvShow.id = Int32(item.id)
                tvShow.name = item.title
                tvShow.overview = item.overview
                tvShow.posterPath = item.posterURL?.lastPathComponent
                tvShow.voteAverage = item.rating
                tvShow.isWatched = true
                tvShow.userRating = item.rating
            }
        }
        
        // Save context
        do {
            try context.save()
            print("✅ Onboarding data saved successfully")
        } catch {
            print("❌ Error saving onboarding data: \(error)")
        }
    }
    
    private func loadSampleContent() {
        // Load popular content for onboarding
        Task {
            do {
                let movies = try await tmdbService.fetchPopularMovies()
                let tvShows = try await tmdbService.fetchPopularTVShows()
                
                await MainActor.run {
                    var content: [OnboardingContentItem] = []
                    
                    // Add movies
                    for movie in movies.prefix(15) {
                        let item = OnboardingContentItem(
                            id: movie.id,
                            title: movie.title,
                            overview: movie.overview ?? "",
                            posterURL: tmdbService.posterURL(for: movie.posterPath),
                            year: movie.releaseDate?.prefix(4).map(String.init),
                            rating: movie.voteAverage,
                            genres: [], // Would need to map genre IDs to names
                            type: .movie
                        )
                        content.append(item)
                    }
                    
                    // Add TV shows
                    for show in tvShows.prefix(15) {
                        let item = OnboardingContentItem(
                            id: show.id,
                            title: show.name,
                            overview: show.overview ?? "",
                            posterURL: tmdbService.posterURL(for: show.posterPath),
                            year: show.firstAirDate?.prefix(4).map(String.init),
                            rating: show.voteAverage,
                            genres: [],
                            type: .tvShow
                        )
                        content.append(item)
                    }
                    
                    // Shuffle for variety
                    self.sampleContent = content.shuffled()
                }
            } catch {
                print("Error loading sample content: \(error)")
                // Fallback to hardcoded content if API fails
                loadFallbackContent()
            }
        }
    }
    
    private func loadFallbackContent() {
        sampleContent = [
            OnboardingContentItem(
                id: 1,
                title: "The Shawshank Redemption",
                overview: "Two imprisoned men bond over a number of years, finding solace and eventual redemption through acts of common decency.",
                posterURL: nil,
                year: "1994",
                rating: 9.3,
                genres: ["Drama"],
                type: .movie
            ),
            OnboardingContentItem(
                id: 2,
                title: "Breaking Bad",
                overview: "A high school chemistry teacher diagnosed with inoperable lung cancer turns to manufacturing and selling methamphetamine.",
                posterURL: nil,
                year: "2008",
                rating: 9.5,
                genres: ["Crime", "Drama", "Thriller"],
                type: .tvShow
            )
            // Add more fallback content as needed
        ]
    }
}

#Preview {
    SwipeCard(
        item: OnboardingContentItem(
            id: 1,
            title: "Sample Movie",
            overview: "This is a sample movie description for the preview.",
            posterURL: nil,
            year: "2024",
            rating: 8.5,
            genres: ["Action", "Adventure"],
            type: .movie
        ),
        isTopCard: true,
        offset: .zero,
        onSwipe: { _ in }
    )
}
