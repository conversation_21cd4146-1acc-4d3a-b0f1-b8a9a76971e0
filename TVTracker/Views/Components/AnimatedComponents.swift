import SwiftUI

// MARK: - Animated Media Card
struct AnimatedMediaCard: View {
    let title: String
    let subtitle: String?
    let imageURL: URL?
    let progress: Double?
    let isWatched: Bool
    let onTap: () -> Void
    
    @State private var isPressed = false
    @State private var imageLoaded = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                // Poster Image
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: 120, height: 180)
                        .overlay(
                            Group {
                                if let imageURL = imageURL {
                                    AsyncImage(url: imageURL) { image in
                                        image
                                            .resizable()
                                            .aspectRatio(contentMode: .fill)
                                            .onAppear {
                                                withAnimation(.easeInOut(duration: 0.3)) {
                                                    imageLoaded = true
                                                }
                                            }
                                    } placeholder: {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                    }
                                } else {
                                    Image(systemName: "photo")
                                        .font(.title2)
                                        .foregroundColor(.gray)
                                }
                            }
                        )
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                        .overlay(
                            // Watched indicator
                            Group {
                                if isWatched {
                                    VStack {
                                        HStack {
                                            Spacer()
                                            Image(systemName: "checkmark.circle.fill")
                                                .foregroundColor(.green)
                                                .background(Color.black.opacity(0.6))
                                                .clipShape(Circle())
                                                .padding(8)
                                        }
                                        Spacer()
                                    }
                                }
                            }
                        )
                        .overlay(
                            // Progress bar for continue watching
                            Group {
                                if let progress = progress, progress > 0 {
                                    VStack {
                                        Spacer()
                                        ProgressView(value: progress)
                                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                                            .scaleEffect(x: 1, y: 0.5)
                                            .padding(.horizontal, 8)
                                            .padding(.bottom, 8)
                                    }
                                }
                            }
                        )
                    
                    // Play button overlay for continue watching
                    if progress != nil {
                        Image(systemName: "play.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .shadow(radius: 4)
                    }
                }
                .scaleEffect(isPressed ? 0.95 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: isPressed)
                
                // Title and subtitle
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.caption)
                        .fontWeight(.medium)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                    
                    if let subtitle = subtitle {
                        Text(subtitle)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                }
                .frame(width: 120, alignment: .leading)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
        .opacity(imageLoaded ? 1.0 : 0.8)
        .animation(.easeInOut(duration: 0.3), value: imageLoaded)
    }
}

// MARK: - Animated Section Header
struct AnimatedSectionHeader: View {
    let title: String
    let subtitle: String?
    let showSeeAll: Bool
    let onSeeAllTap: (() -> Void)?
    
    @State private var isVisible = false
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.title3)
                    .fontWeight(.semibold)
                    .offset(x: isVisible ? 0 : -20)
                    .opacity(isVisible ? 1 : 0)
                
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .offset(x: isVisible ? 0 : -20)
                        .opacity(isVisible ? 1 : 0)
                }
            }
            
            Spacer()
            
            if showSeeAll {
                Button(action: onSeeAllTap ?? {}) {
                    Text("See All")
                        .font(.caption)
                        .foregroundColor(.blue)
                        .opacity(isVisible ? 1 : 0)
                }
            }
        }
        .padding(.horizontal)
        .onAppear {
            withAnimation(.easeOut(duration: 0.6).delay(0.1)) {
                isVisible = true
            }
        }
    }
}

// MARK: - Animated Tab Transition
struct AnimatedTabTransition: ViewModifier {
    let isActive: Bool
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isActive ? 1.0 : 0.95)
            .opacity(isActive ? 1.0 : 0.8)
            .animation(.easeInOut(duration: 0.2), value: isActive)
    }
}

extension View {
    func animatedTabTransition(isActive: Bool) -> some View {
        modifier(AnimatedTabTransition(isActive: isActive))
    }
}

// MARK: - Floating Action Button
struct FloatingActionButton: View {
    let icon: String
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.white)
                .frame(width: 56, height: 56)
                .background(
                    Circle()
                        .fill(Color.blue)
                        .shadow(color: .blue.opacity(0.3), radius: 8, x: 0, y: 4)
                )
        }
        .scaleEffect(isPressed ? 0.9 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

// MARK: - Shimmer Loading Effect
struct ShimmerView: View {
    @State private var isAnimating = false
    
    var body: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(
                LinearGradient(
                    colors: [
                        Color.gray.opacity(0.3),
                        Color.gray.opacity(0.1),
                        Color.gray.opacity(0.3)
                    ],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .frame(width: 120, height: 180)
            .offset(x: isAnimating ? 200 : -200)
            .onAppear {
                withAnimation(.linear(duration: 1.5).repeatForever(autoreverses: false)) {
                    isAnimating = true
                }
            }
    }
}

// MARK: - Animated Rating Stars
struct AnimatedRatingStars: View {
    let rating: Double
    let maxRating: Int = 5
    
    @State private var animatedRating: Double = 0
    
    var body: some View {
        HStack(spacing: 2) {
            ForEach(0..<maxRating, id: \.self) { index in
                Image(systemName: starType(for: index))
                    .foregroundColor(.yellow)
                    .font(.caption)
                    .scaleEffect(animatedRating > Double(index) ? 1.0 : 0.5)
                    .animation(.easeOut(duration: 0.3).delay(Double(index) * 0.1), value: animatedRating)
            }
        }
        .onAppear {
            withAnimation {
                animatedRating = rating
            }
        }
    }
    
    private func starType(for index: Int) -> String {
        let starValue = animatedRating - Double(index)
        if starValue >= 1.0 {
            return "star.fill"
        } else if starValue >= 0.5 {
            return "star.leadinghalf.filled"
        } else {
            return "star"
        }
    }
}

// MARK: - Animated Progress Ring
struct AnimatedProgressRing: View {
    let progress: Double
    let lineWidth: CGFloat = 4
    
    @State private var animatedProgress: Double = 0
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(Color.gray.opacity(0.3), lineWidth: lineWidth)
            
            Circle()
                .trim(from: 0, to: animatedProgress)
                .stroke(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    style: StrokeStyle(lineWidth: lineWidth, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1.0), value: animatedProgress)
        }
        .onAppear {
            animatedProgress = progress
        }
    }
}

// MARK: - Bounce Animation Modifier
struct BounceAnimation: ViewModifier {
    @State private var bouncing = false
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(bouncing ? 1.1 : 1.0)
            .animation(.easeInOut(duration: 0.6).repeatForever(autoreverses: true), value: bouncing)
            .onAppear {
                bouncing = true
            }
    }
}

extension View {
    func bounceAnimation() -> some View {
        modifier(BounceAnimation())
    }
}

// MARK: - Slide In Animation
struct SlideInAnimation: ViewModifier {
    let delay: Double
    @State private var isVisible = false
    
    func body(content: Content) -> some View {
        content
            .offset(y: isVisible ? 0 : 50)
            .opacity(isVisible ? 1 : 0)
            .onAppear {
                withAnimation(.easeOut(duration: 0.6).delay(delay)) {
                    isVisible = true
                }
            }
    }
}

extension View {
    func slideInAnimation(delay: Double = 0) -> some View {
        modifier(SlideInAnimation(delay: delay))
    }
}
