<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22225" systemVersion="23A344" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Episode" representedClassName="Episode" syncable="YES" codeGenerationType="class">
        <attribute name="airDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="episodeNumber" optional="YES" attributeType="Integer 32" defaultValue="0" usesScalarValueType="YES"/>
        <attribute name="id" optional="YES" attributeType="Integer 32" defaultValue="0" usesScalarValueType="YES"/>
        <attribute name="isWatched" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="overview" optional="YES" attributeType="String"/>
        <attribute name="runtime" optional="YES" attributeType="Integer 32" defaultValue="0" usesScalarValueType="YES"/>
        <attribute name="seasonNumber" optional="YES" attributeType="Integer 32" defaultValue="0" usesScalarValueType="YES"/>
        <attribute name="stillPath" optional="YES" attributeType="String"/>
        <attribute name="userRating" optional="YES" attributeType="Double" defaultValue="0.0" usesScalarValueType="YES"/>
        <attribute name="voteAverage" optional="YES" attributeType="Double" defaultValue="0.0" usesScalarValueType="YES"/>
        <attribute name="watchedDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="ckRecordID" optional="YES" attributeType="String"/>
        <attribute name="ckRecordSystemFields" optional="YES" attributeType="Binary"/>
        <relationship name="tvShow" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="TVShow" inverseName="episodes" inverseEntity="TVShow"/>
    </entity>
    <entity name="Movie" representedClassName="Movie" syncable="YES" codeGenerationType="class">
        <attribute name="backdropPath" optional="YES" attributeType="String"/>
        <attribute name="genres" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="Integer 32" defaultValue="0" usesScalarValueType="YES"/>
        <attribute name="isWatched" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="overview" optional="YES" attributeType="String"/>
        <attribute name="posterPath" optional="YES" attributeType="String"/>
        <attribute name="releaseDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="runtime" optional="YES" attributeType="Integer 32" defaultValue="0" usesScalarValueType="YES"/>
        <attribute name="title" optional="YES" attributeType="String"/>
        <attribute name="userRating" optional="YES" attributeType="Double" defaultValue="0.0" usesScalarValueType="YES"/>
        <attribute name="voteAverage" optional="YES" attributeType="Double" defaultValue="0.0" usesScalarValueType="YES"/>
        <attribute name="watchedDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="ckRecordID" optional="YES" attributeType="String"/>
        <attribute name="ckRecordSystemFields" optional="YES" attributeType="Binary"/>
        <relationship name="watchHistory" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="WatchHistory" inverseName="movie" inverseEntity="WatchHistory"/>
    </entity>
    <entity name="TVShow" representedClassName="TVShow" syncable="YES" codeGenerationType="class">
        <attribute name="backdropPath" optional="YES" attributeType="String"/>
        <attribute name="firstAirDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="genres" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="Integer 32" defaultValue="0" usesScalarValueType="YES"/>
        <attribute name="isWatched" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="lastAirDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="numberOfEpisodes" optional="YES" attributeType="Integer 32" defaultValue="0" usesScalarValueType="YES"/>
        <attribute name="numberOfSeasons" optional="YES" attributeType="Integer 32" defaultValue="0" usesScalarValueType="YES"/>
        <attribute name="overview" optional="YES" attributeType="String"/>
        <attribute name="posterPath" optional="YES" attributeType="String"/>
        <attribute name="status" optional="YES" attributeType="String"/>
        <attribute name="userRating" optional="YES" attributeType="Double" defaultValue="0.0" usesScalarValueType="YES"/>
        <attribute name="voteAverage" optional="YES" attributeType="Double" defaultValue="0.0" usesScalarValueType="YES"/>
        <attribute name="ckRecordID" optional="YES" attributeType="String"/>
        <attribute name="ckRecordSystemFields" optional="YES" attributeType="Binary"/>
        <relationship name="episodes" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Episode" inverseName="tvShow" inverseEntity="Episode"/>
        <relationship name="watchHistory" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="WatchHistory" inverseName="tvShow" inverseEntity="WatchHistory"/>
    </entity>
    <entity name="WatchHistory" representedClassName="WatchHistory" syncable="YES" codeGenerationType="class">
        <attribute name="dateWatched" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="progress" optional="YES" attributeType="Double" defaultValue="0.0" usesScalarValueType="YES"/>
        <attribute name="userRating" optional="YES" attributeType="Double" defaultValue="0.0" usesScalarValueType="YES"/>
        <attribute name="ckRecordID" optional="YES" attributeType="String"/>
        <attribute name="ckRecordSystemFields" optional="YES" attributeType="Binary"/>
        <relationship name="episode" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Episode"/>
        <relationship name="movie" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Movie" inverseName="watchHistory" inverseEntity="Movie"/>
        <relationship name="tvShow" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="TVShow" inverseName="watchHistory" inverseEntity="TVShow"/>
    </entity>
</model>
