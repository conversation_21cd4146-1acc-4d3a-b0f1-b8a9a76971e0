# TVTracker Development Setup Guide

This comprehensive guide will walk you through setting up, building, running, and debugging the TVTracker iOS app locally on your development machine.

## Prerequisites

### Required Software
- **macOS**: 13.0 (Ventura) or later
- **Xcode**: 15.0 or later
- **iOS Simulator**: iOS 17.0+ or physical device
- **Git**: For version control (usually pre-installed)

### Required Accounts
- **Apple Developer Account**: Free account sufficient for local development
- **TMDB Account**: Free account for API access
- **OpenAI Account**: For AI-powered recommendations
- **iCloud Account**: For testing CloudKit sync (optional)

### Hardware Requirements
- **Mac**: Intel or Apple Silicon
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space minimum
- **iOS Device**: iPhone/iPad with iOS 17.0+ (optional, for device testing)

## Step 1: Environment Setup

### 1.1 Install Xcode
1. **Download from App Store**:
   - Open Mac App Store
   - Search for "Xcode"
   - Install Xcode (this may take 30-60 minutes)

2. **Install Command Line Tools**:
   ```bash
   xcode-select --install
   ```

3. **Verify Installation**:
   ```bash
   xcode-select -p
   # Should output: /Applications/Xcode.app/Contents/Developer
   ```

### 1.2 Configure Xcode
1. **Open Xcode**
2. **Add Apple ID**:
   - Xcode → Preferences → Accounts
   - Click "+" → Apple ID
   - Sign in with your Apple ID

3. **Download iOS Simulators**:
   - Xcode → Preferences → Components
   - Download iOS 17.0+ simulators

## Step 2: Project Setup

### 2.1 Clone or Create Project
If you have the project files in a directory:

```bash
# Navigate to your project directory
cd /path/to/tv-tracker

# Verify project structure
ls -la
# Should see: TVTracker/, README.md, etc.
```

### 2.2 Create Xcode Project
Since we have the source files but need an Xcode project:

1. **Open Xcode**
2. **Create New Project**:
   - File → New → Project
   - iOS → App
   - Product Name: `TVTracker`
   - Interface: SwiftUI
   - Language: Swift
   - Use Core Data: ✓
   - Bundle Identifier: `com.tvtracker.app`

3. **Replace Generated Files**:
   - Delete generated `ContentView.swift` and `TVTrackerApp.swift`
   - Copy all files from `TVTracker/` folder into Xcode project
   - Add files to Xcode project (drag and drop)

### 2.3 Project Structure Verification
Your Xcode project should contain:

```
TVTracker/
├── TVTrackerApp.swift              # Main app file
├── ContentView.swift               # Root view
├── Services/
│   ├── TMDBService.swift          # API service
│   └── RecommendationEngine.swift # AI recommendations
├── Views/
│   ├── Components/
│   │   └── AnimatedComponents.swift
│   ├── DetailView.swift
│   ├── SearchView.swift
│   └── CloudKitSyncView.swift
├── DataModel.xcdatamodeld/        # Core Data model
└── Assets.xcassets/               # App assets
```

## Step 3: API Configuration

### 3.1 Get TMDB API Key
1. **Create TMDB Account**:
   - Go to [themoviedb.org](https://www.themoviedb.org/)
   - Sign up for free account

2. **Request API Key**:
   - Go to Settings → API
   - Click "Create" → "Developer"
   - Fill out application form
   - Accept terms and submit

3. **Copy API Key**:
   - Copy the "API Key (v3 auth)" value

### 3.2 Get OpenAI API Key
1. **Create OpenAI Account**:
   - Go to [platform.openai.com](https://platform.openai.com/)
   - Sign up for account

2. **Generate API Key**:
   - Go to API Keys section
   - Click "Create new secret key"
   - Copy the generated key immediately (you won't see it again)

3. **Add Credits** (if needed):
   - OpenAI requires payment for API usage
   - Add billing information and credits
   - Monitor usage to avoid unexpected charges

### 3.3 Configure API Keys
1. **Configure TMDB API Key**:
   - Open `TVTracker/Services/TMDBService.swift`
   - Replace placeholder:
   ```swift
   private let apiKey = "YOUR_TMDB_API_KEY" // Replace with actual key
   ```

   With your actual key:
   ```swift
   private let apiKey = "your_actual_tmdb_api_key_here"
   ```

2. **Configure OpenAI API Key**:
   - Open `TVTracker/Services/OpenAIService.swift`
   - Replace placeholder:
   ```swift
   private let apiKey = "YOUR_OPENAI_API_KEY" // Replace with actual key
   ```

   With your actual key:
   ```swift
   private let apiKey = "your_actual_openai_api_key_here"
   ```

⚠️ **Security Note**: Never commit API keys to version control. Consider using environment variables or configuration files.

⚠️ **Cost Warning**: OpenAI API usage incurs costs. Monitor your usage and set billing limits to avoid unexpected charges.

## Step 4: CloudKit Setup (Optional)

### 4.1 Enable CloudKit Capability
1. **Select Project in Xcode**
2. **Select TVTracker Target**
3. **Go to Signing & Capabilities**
4. **Add CloudKit Capability**:
   - Click "+ Capability"
   - Search "CloudKit"
   - Add CloudKit

5. **Configure Container**:
   - Container: `iCloud.com.tvtracker.app`
   - Or create custom container

### 4.2 Configure Development Team
1. **Select your development team**
2. **Ensure bundle identifier is unique**
3. **Xcode will handle provisioning profiles**

For detailed CloudKit setup, see [CloudKit-Setup-Guide.md](CloudKit-Setup-Guide.md).

## Step 5: Building the Project

### 5.1 Initial Build
1. **Select Target**:
   - Choose iPhone simulator or connected device
   - Recommended: iPhone 15 Pro simulator

2. **Build Project**:
   - Press ⌘+B or Product → Build
   - Wait for build to complete

### 5.2 Resolve Build Issues

**Common Issues and Solutions**:

**Missing Files Error**:
```
error: No such file or directory
```
- Ensure all source files are added to Xcode project
- Check file paths and references

**API Key Error**:
```
error: Use of unresolved identifier 'YOUR_TMDB_API_KEY'
```
- Replace placeholder with actual TMDB API key

**CloudKit Errors**:
```
error: CloudKit capability not configured
```
- Add CloudKit capability in project settings
- Ensure development team is selected

**Core Data Errors**:
```
error: Core Data model not found
```
- Ensure DataModel.xcdatamodeld is added to project
- Check Core Data model configuration

## Step 6: Running the App

### 6.1 Run on Simulator
1. **Select Simulator**:
   - Choose iPhone 15 Pro (iOS 17.0+)
   - Or any iOS 17.0+ simulator

2. **Run App**:
   - Press ⌘+R or Product → Run
   - App should launch in simulator

### 6.2 Run on Physical Device
1. **Connect Device**:
   - Connect iPhone/iPad via USB
   - Trust computer if prompted

2. **Select Device**:
   - Choose your device from target list

3. **Run App**:
   - Press ⌘+R
   - App will install and launch on device

### 6.3 First Launch Verification
When app launches successfully, you should see:
- Tab bar with 5 tabs (Home, Movies, TV Shows, Search, Profile)
- Home screen with welcome message
- Placeholder content cards
- Functional navigation between tabs

## Step 7: Testing Core Features

### 7.1 Test TMDB Integration
1. **Go to Search Tab**
2. **Search for a movie**: e.g., "Inception"
3. **Verify results appear**
4. **Check network requests in console**

### 7.2 Test Core Data
1. **Add items to watchlist** (when implemented)
2. **Mark items as watched**
3. **Verify data persists** after app restart

### 7.3 Test OpenAI Integration
1. **Go to AI tab** in the app
2. **Add some movies/TV shows to your watchlist**
3. **Rate content** to build viewing history
4. **Check AI recommendations** are generated
5. **Verify personalized insights** appear

### 7.4 Test CloudKit Sync (if enabled)
1. **Go to Profile → iCloud Sync**
2. **Check account status**
3. **Verify sync functionality**

## Step 8: Debugging

### 8.1 Xcode Debugger
**Set Breakpoints**:
1. Click line number in code editor
2. Red dot appears indicating breakpoint
3. Run app, execution will pause at breakpoint

**Debug Console**:
- View → Debug Area → Activate Console
- Use `print()` statements for logging
- View variable values in debug area

### 8.2 Common Debugging Scenarios

**API Issues**:
```swift
// Add logging to TMDBService.swift
print("API Request URL: \(url)")
print("API Response: \(data)")
```

**Core Data Issues**:
```swift
// Add to PersistenceController
print("Core Data container loaded: \(container)")
print("Context: \(container.viewContext)")
```

**CloudKit Issues**:
```swift
// Add to CloudKitSyncManager
print("CloudKit account status: \(accountStatus)")
print("Sync error: \(error)")
```

### 8.3 Simulator Debugging
**Reset Simulator**:
- Device → Erase All Content and Settings
- Useful for testing fresh installs

**Simulator Logs**:
- Window → Devices and Simulators
- Select simulator → Open Console

### 8.4 Device Debugging
**Device Console**:
- Window → Devices and Simulators
- Select device → Open Console
- Filter by app name: "TVTracker"

**Crash Logs**:
- Xcode → Window → Organizer
- Crashes tab shows crash reports

## Step 9: Performance Optimization

### 9.1 Instruments
**Profile App Performance**:
1. Product → Profile (⌘+I)
2. Choose instrument:
   - Time Profiler: CPU usage
   - Allocations: Memory usage
   - Network: API calls
   - Core Data: Database performance

### 9.2 Memory Management
**Check for Memory Leaks**:
- Use Allocations instrument
- Look for growing memory usage
- Check retain cycles in closures

### 9.3 Network Optimization
**Monitor API Calls**:
- Use Network instrument
- Check request frequency
- Implement proper caching

## Step 10: Advanced Development

### 10.1 SwiftUI Previews
**Enable Live Previews**:
1. Ensure preview code exists:
   ```swift
   #Preview {
       ContentView()
   }
   ```
2. Click "Resume" in preview canvas
3. Use for rapid UI development

### 10.2 Unit Testing
**Create Test Target**:
1. File → New → Target
2. iOS → Unit Testing Bundle
3. Add test files for core functionality

### 10.3 UI Testing
**Create UI Test Target**:
1. File → New → Target
2. iOS → UI Testing Bundle
3. Record and create UI tests

## Troubleshooting

### Common Issues

**Build Fails**:
- Clean build folder: Product → Clean Build Folder (⌘+Shift+K)
- Restart Xcode
- Check for syntax errors

**Simulator Issues**:
- Reset simulator content
- Try different simulator models
- Restart simulator

**CloudKit Issues**:
- Check iCloud account status
- Verify CloudKit capability
- Check CloudKit Dashboard

**API Issues**:
- Verify API key is correct
- Check network connectivity
- Monitor API rate limits

### Getting Help

**Apple Documentation**:
- [SwiftUI Documentation](https://developer.apple.com/documentation/swiftui)
- [Core Data Documentation](https://developer.apple.com/documentation/coredata)
- [CloudKit Documentation](https://developer.apple.com/documentation/cloudkit)

**Community Resources**:
- Stack Overflow
- Apple Developer Forums
- Swift Forums

**Project-Specific**:
- Check README.md for project overview
- Review CloudKit-Setup-Guide.md for sync setup
- Examine code comments for implementation details

## Step 11: Development Workflow

### 11.1 Version Control Setup
**Initialize Git Repository**:
```bash
cd /path/to/tv-tracker
git init
git add .
git commit -m "Initial commit"
```

**Create .gitignore**:
```gitignore
# Xcode
*.xcodeproj/*
!*.xcodeproj/project.pbxproj
!*.xcodeproj/xcshareddata/
!*.xcworkspace/contents.xcworkspacedata
/*.gcno
**/xcshareddata/WorkspaceSettings.xcsettings

# API Keys (security)
**/APIKeys.swift
**/Config.plist
.local.env

# Build products
build/
DerivedData/

# CocoaPods
Pods/
*.xcworkspace

# Carthage
Carthage/Build/

# Swift Package Manager
.swiftpm/
Packages/
Package.resolved

# macOS
.DS_Store
```

### 11.2 Code Organization Best Practices
**File Structure**:
- Keep related files grouped in folders
- Use meaningful file names
- Separate UI, business logic, and data layers

**Naming Conventions**:
- Use descriptive variable names
- Follow Swift naming conventions
- Add comments for complex logic

### 11.3 Testing Strategy
**Unit Tests**:
```swift
// Example test for TMDBService
import XCTest
@testable import TVTracker

class TMDBServiceTests: XCTestCase {
    func testMovieSearch() async throws {
        let service = TMDBService.shared
        let results = try await service.searchMulti(query: "Inception")
        XCTAssertFalse(results.isEmpty)
    }
}
```

**UI Tests**:
```swift
// Example UI test
import XCTest

class TVTrackerUITests: XCTestCase {
    func testTabNavigation() throws {
        let app = XCUIApplication()
        app.launch()

        app.tabBars.buttons["Movies"].tap()
        XCTAssertTrue(app.navigationBars["Movies"].exists)
    }
}
```

## Step 12: Deployment Preparation

### 12.1 App Store Preparation
**App Icons**:
- Create app icons for all required sizes
- Use vector graphics for scalability
- Follow Apple's icon design guidelines

**Launch Screen**:
- Design appropriate launch screen
- Keep it simple and fast-loading
- Match app's visual style

**Privacy Policy**:
- Required for CloudKit usage
- Include data collection practices
- Host on accessible website

### 12.2 Build Configurations
**Debug vs Release**:
- Debug: Development with logging
- Release: Optimized for App Store

**Scheme Configuration**:
- Archive scheme uses Release configuration
- Enable optimizations for release builds

## Step 13: Advanced Debugging Techniques

### 13.1 Network Debugging
**Charles Proxy Setup**:
1. Install Charles Proxy
2. Configure iOS simulator proxy
3. Monitor TMDB API calls
4. Debug request/response data

**Network Link Conditioner**:
- Simulate poor network conditions
- Test app behavior with slow/unreliable connections
- Available in Additional Tools for Xcode

### 13.2 Core Data Debugging
**Core Data Debug Flags**:
```swift
// Add to launch arguments in scheme
-com.apple.CoreData.SQLDebug 1
-com.apple.CoreData.Logging.stderr 1
```

**Core Data Model Viewer**:
- Use Core Data Model Editor in Xcode
- Visualize entity relationships
- Validate model consistency

### 13.3 CloudKit Debugging
**CloudKit Console Logging**:
```swift
// Enable CloudKit logging
UserDefaults.standard.set(true, forKey: "CKDebugLogging")
```

**CloudKit Dashboard**:
- Monitor sync operations
- View schema and data
- Check for sync errors

## Step 14: Performance Monitoring

### 14.1 App Performance
**Key Metrics to Monitor**:
- App launch time
- Memory usage
- CPU usage
- Network requests
- Battery usage

**Instruments Templates**:
- Time Profiler: CPU performance
- Allocations: Memory leaks
- Leaks: Memory management
- Network: API performance
- Energy Log: Battery impact

### 14.2 User Experience Metrics
**Animation Performance**:
- Target 60 FPS for smooth animations
- Use Instruments to measure frame rates
- Optimize heavy animations

**Responsiveness**:
- Keep main thread free
- Use async/await for network calls
- Implement proper loading states

## Step 15: Continuous Integration (Optional)

### 15.1 GitHub Actions Setup
**Create .github/workflows/ios.yml**:
```yaml
name: iOS CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: macos-latest

    steps:
    - uses: actions/checkout@v3

    - name: Build
      run: |
        xcodebuild -project TVTracker.xcodeproj \
                   -scheme TVTracker \
                   -destination 'platform=iOS Simulator,name=iPhone 15' \
                   build

    - name: Test
      run: |
        xcodebuild -project TVTracker.xcodeproj \
                   -scheme TVTracker \
                   -destination 'platform=iOS Simulator,name=iPhone 15' \
                   test
```

### 15.2 Automated Testing
**Test Automation**:
- Run tests on every commit
- Generate test reports
- Monitor test coverage

## Appendix: Quick Reference

### Essential Xcode Shortcuts
- **⌘+R**: Run app
- **⌘+B**: Build project
- **⌘+Shift+K**: Clean build folder
- **⌘+I**: Profile with Instruments
- **⌘+U**: Run tests
- **⌘+Shift+O**: Open quickly
- **⌘+/**: Comment/uncomment
- **⌘+Shift+A**: Show actions

### Useful Console Commands
```bash
# View simulator logs
xcrun simctl spawn booted log stream --predicate 'subsystem contains "TVTracker"'

# Reset simulator
xcrun simctl erase all

# List available simulators
xcrun simctl list devices

# Install app on simulator
xcrun simctl install booted /path/to/TVTracker.app
```

### Debug Print Statements
```swift
// Network debugging
print("🌐 API Request: \(url)")
print("📦 Response Data: \(data)")

// Core Data debugging
print("💾 Saving context: \(context)")
print("🔍 Fetch request: \(request)")

// CloudKit debugging
print("☁️ CloudKit status: \(status)")
print("🔄 Sync operation: \(operation)")

// UI debugging
print("🎨 View appeared: \(self)")
print("👆 Button tapped: \(action)")
```

---

**Happy Coding!** 🚀

This comprehensive guide covers everything you need to set up, build, run, and debug the TVTracker project locally. Remember to:

- **Start Simple**: Get basic functionality working first
- **Test Early**: Test on both simulator and device regularly
- **Debug Systematically**: Use proper debugging tools and techniques
- **Document Changes**: Keep track of modifications and improvements
- **Stay Updated**: Keep Xcode and dependencies current

For additional help, refer to the project's README.md and CloudKit-Setup-Guide.md files, or consult Apple's extensive developer documentation.
