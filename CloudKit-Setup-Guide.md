# CloudKit Setup Guide for TVTracker

This guide will help you set up iCloud sync functionality for the TVTracker app using CloudKit.

## Prerequisites

- Apple Developer Account (free or paid)
- Xcode 15.0 or later
- iOS device or simulator with iOS 17.0+
- iCloud account for testing

## Step 1: Enable CloudKit in Xcode

1. **Open your TVTracker project in Xcode**

2. **Select your project in the navigator**
   - Click on the project name at the top of the file navigator

3. **Select your app target**
   - Choose "TVTracker" under TARGETS

4. **Go to Signing & Capabilities tab**

5. **Add CloudKit capability**
   - Click the "+ Capability" button
   - Search for "CloudKit"
   - Add "CloudKit" capability

6. **Configure CloudKit Container**
   - A default container will be created: `iCloud.com.tvtracker.app`
   - You can use this or create a custom one

## Step 2: Configure Core Data for CloudKit

The app is already configured to use `NSPersistentCloudKitContainer`. Here's what's included:

### Core Data Model Updates
- All entities now include CloudKit-required attributes:
  - `ckRecordID` (String) - CloudKit record identifier
  - `ckRecordSystemFields` (Binary) - CloudKit system metadata

### Supported Entities
- **Movie**: User's movie watchlist and ratings
- **TVShow**: User's TV show watchlist and ratings  
- **Episode**: Individual episode watch status
- **WatchHistory**: Detailed viewing history and progress

## Step 3: CloudKit Dashboard Configuration

1. **Access CloudKit Dashboard**
   - Go to [CloudKit Dashboard](https://icloud.developer.apple.com/)
   - Sign in with your Apple Developer account

2. **Select your container**
   - Choose `iCloud.com.tvtracker.app` (or your custom container)

3. **Configure Schema (Development Environment)**
   - The schema will be automatically created when you first run the app
   - Core Data will push the schema to CloudKit

4. **Deploy to Production**
   - After testing in development, deploy schema to production
   - Go to Schema → Deploy Schema Changes
   - Deploy from Development to Production

## Step 4: App Configuration

### Bundle Identifier
Ensure your bundle identifier matches the CloudKit container:
```
com.tvtracker.app
```

### Entitlements
The following entitlements are automatically added:
- `com.apple.developer.icloud-services` = CloudKit
- `com.apple.developer.icloud-container-identifiers` = iCloud.com.tvtracker.app

## Step 5: Testing CloudKit Sync

### Development Testing
1. **Run the app on a device or simulator**
2. **Sign in to iCloud** in Settings
3. **Open TVTracker app**
4. **Go to Profile → iCloud Sync**
5. **Check account status** - should show "Signed in and ready to sync"

### Adding Test Data
1. **Add movies/TV shows to your watchlist**
2. **Mark items as watched**
3. **Rate content**
4. **Check sync status** in the iCloud Sync view

### Multi-Device Testing
1. **Install app on multiple devices** with same iCloud account
2. **Add data on one device**
3. **Force sync** using the sync button
4. **Check other devices** for synced data

## Step 6: Sync Features

### Automatic Sync
- Data syncs automatically when:
  - App launches
  - Data is modified
  - Network becomes available
  - App returns from background

### Manual Sync
- Users can force sync from Profile → iCloud Sync
- Useful for immediate synchronization

### Sync Status Monitoring
The app provides real-time sync status:
- Account status (signed in, not signed in, etc.)
- Sync progress indicators
- Last sync timestamp
- Sync statistics (items synced)

## Step 7: Troubleshooting

### Common Issues

**"Not signed in to iCloud"**
- Check device iCloud settings
- Ensure iCloud Drive is enabled
- Sign out and back in to iCloud

**"Sync not working"**
- Check network connection
- Verify CloudKit container configuration
- Check CloudKit Dashboard for errors

**"Schema errors"**
- Ensure Core Data model matches CloudKit schema
- Reset development schema if needed
- Redeploy schema changes

### Debug CloudKit Issues

1. **Enable CloudKit logging**
   ```swift
   // Add to AppDelegate or App struct
   UserDefaults.standard.set(true, forKey: "CKDebugLogging")
   ```

2. **Check CloudKit Dashboard logs**
   - Go to Logs section in CloudKit Dashboard
   - Filter by your container and time range

3. **Monitor sync status**
   - Use the built-in sync status view
   - Check console logs for CloudKit errors

## Step 8: Privacy and Data Handling

### User Privacy
- All data is stored in the user's private CloudKit database
- No data is shared between users
- Users control their own sync settings

### Data Types Synced
- **Movies**: Title, watch status, user rating, watch date
- **TV Shows**: Name, watch status, user rating, episode progress
- **Episodes**: Individual episode watch status and progress
- **Watch History**: Detailed viewing history and timestamps

### Data Not Synced
- TMDB API responses (cached locally only)
- App preferences and settings
- Temporary UI state

## Step 9: Production Deployment

### Before App Store Submission
1. **Deploy CloudKit schema to production**
2. **Test with production CloudKit environment**
3. **Verify all sync functionality works**
4. **Test with multiple Apple IDs**

### App Store Review
- CloudKit apps require additional review time
- Ensure privacy policy mentions iCloud sync
- Test thoroughly on clean devices

## Step 10: Monitoring and Maintenance

### CloudKit Usage Monitoring
- Monitor CloudKit usage in Developer Portal
- Track sync performance and errors
- Plan for scaling if needed

### Schema Updates
- Plan schema migrations carefully
- Test migrations thoroughly
- Consider backward compatibility

## Support and Resources

### Apple Documentation
- [CloudKit Documentation](https://developer.apple.com/documentation/cloudkit)
- [Core Data with CloudKit](https://developer.apple.com/documentation/coredata/mirroring_a_core_data_store_with_cloudkit)

### TVTracker Specific Help
- Check the app's iCloud Sync view for status
- Use force sync for immediate synchronization
- Reset cloud data if sync issues persist

---

**Note**: CloudKit sync requires an active internet connection and iCloud account. Users can still use the app offline, with sync occurring when connectivity is restored.
